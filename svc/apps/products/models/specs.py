from sqlalchemy import (JSON, BigInteger, Column, Float, Foreign<PERSON>ey, Integer,
                        String, UniqueConstraint)
from sqlalchemy.orm import relationship

from svc.apps.albums.models.album import Album
from svc.core.models.base import Base


class Spec(Base):
    """
    规格定义模型，如"颜色"、"尺寸"
    """
    __tablename__ = "specs"
    __table_args__ = (UniqueConstraint("name", name="uq_spec_name"),)
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="规格名称")
    # 可扩展：描述、排序等
    options = relationship("SpecOption", back_populates="spec", cascade="all, delete-orphan", lazy="selectin")

class SpecOption(Base):
    """
    规格值模型，如"红色"、"XL"
    """
    __tablename__ = "spec_options"
    __table_args__ = (UniqueConstraint("spec_id", "value", name="uq_specoption_specid_value"),)
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    spec_id = Column(BigInteger, ForeignKey("specs.id"), nullable=False)
    value = Column(String(100), nullable=False, comment="规格值")
    spec = relationship("Spec", back_populates="options")
    # 可扩展：图片、排序等

class ProductSpec(Base):
    """
    商品与规格的关联
    """
    __tablename__ = "product_specs"
    __table_args__ = (UniqueConstraint("product_id", "spec_id", name="uq_productspec_productid_specid"),)
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    product_id = Column(BigInteger, ForeignKey("products.id"), nullable=False)
    spec_id = Column(BigInteger, ForeignKey("specs.id"), nullable=False)
    # 可扩展：排序等

class ProductSpecOption(Base):
    """
    商品与规格值的关联
    """
    __tablename__ = "product_spec_options"
    __table_args__ = (UniqueConstraint("product_spec_id", "spec_option_id", name="uq_productspecoption_psid_soid"),)
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    product_spec_id = Column(BigInteger, ForeignKey("product_specs.id"), nullable=False)
    spec_option_id = Column(BigInteger, ForeignKey("spec_options.id"), nullable=False)

class ProductSpecCombination(Base):
    """
    商品规格组合（可售单元），如"红色-XL"
    """
    __tablename__ = "product_spec_combinations"
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    product_id = Column(BigInteger, ForeignKey("products.id"), nullable=False)
    sku = Column(String(100), nullable=False, unique=True, index=True, comment="SKU")
    price = Column(Float, nullable=True, comment="价格")
    stock_quantity = Column(Integer, default=0, comment="库存数量")
    spec_option_ids = Column(JSON, default=list, comment="该组合包含的所有规格值ID")
    album_id = Column(BigInteger, ForeignKey("albums.id"), nullable=True, unique=True, comment="关联图册ID")
    
    # 关系
    album = relationship("Album", lazy="selectin", uselist=False, foreign_keys=[album_id])
    # 可扩展：图片、条码、是否启用等 