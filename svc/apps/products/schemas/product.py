from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from svc.apps.albums.schemas.album import AlbumResponse, UserAlbumResponse
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.base import PaginatedResponse


class ProductBase(CamelCaseModel):
    """商品基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "iPhone 15 Pro",
                "description": "苹果最新旗舰手机",
                "short_description": "A17 Pro芯片，钛金属设计",
                "sku": "IPHONE15PRO001",
                "barcode": "1234567890123",
                "category_id": 1,
                "price": 7999.00,
                "cost_price": 5000.00,
                "market_price": 8999.00,
                "currency": "CNY",
                "is_featured": True,
                "is_digital": False,
                "track_inventory": True,
                "stock_quantity": 100,
                "min_stock_level": 10,
                "max_stock_level": 1000,
                "weight": 0.187,
                "dimensions": {"length": 159.9, "width": 76.7, "height": 8.25},
                "attributes": {"color": "钛原色", "storage": "128GB"},
                "seo_title": "iPhone 15 Pro - 苹果官方商城",
                "seo_description": "购买全新iPhone 15 Pro，A17 Pro芯片",
                "seo_keywords": "iPhone,苹果,手机,A17 Pro",
                "rich_description": "<p>详细介绍内容</p>"
            }
        }
    )
    
    name: str = Field(..., description="商品名称")
    description: Optional[str] = Field(default=None, description="商品描述")
    short_description: Optional[str] = Field(default=None, description="商品简短描述")
    sku: str = Field(..., description="商品SKU")
    barcode: Optional[str] = Field(default=None, description="商品条码")
    category_id: Optional[int] = Field(default=None, description="分类ID")
    price: float = Field(..., description="商品价格")
    cost_price: Optional[float] = Field(default=None, description="成本价格")
    market_price: Optional[float] = Field(default=None, description="市场价格")
    currency: str = Field(default="CNY", description="货币单位")
    is_featured: bool = Field(default=False, description="是否为推荐商品")
    is_digital: bool = Field(default=False, description="是否为数字商品")
    track_inventory: bool = Field(default=True, description="是否跟踪库存")
    stock_quantity: int = Field(default=0, description="库存数量")
    min_stock_level: int = Field(default=0, description="最低库存警戒线")
    max_stock_level: Optional[int] = Field(default=None, description="最高库存限制")
    weight: Optional[float] = Field(default=None, description="重量(kg)")
    dimensions: Dict[str, Any] = Field(default_factory=dict, description="尺寸信息")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="商品属性")
    seo_title: Optional[str] = Field(default=None, description="SEO标题")
    seo_description: Optional[str] = Field(default=None, description="SEO描述")
    seo_keywords: Optional[str] = Field(default=None, description="SEO关键词")
    rich_description: Optional[str] = Field(default=None, description="产品富文本详情")

class ProductCreate(ProductBase):
    """商品创建模型"""
    pass

class ProductUpdate(CamelCaseModel):
    """商品更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "iPhone 15 Pro Max",
                "price": 8999.00,
                "status": "active",
                "rich_description": "<p>详细介绍内容</p>"
            }
        }
    )
    
    name: Optional[str] = Field(default=None, description="商品名称")
    description: Optional[str] = Field(default=None, description="商品描述")
    short_description: Optional[str] = Field(default=None, description="商品简短描述")
    sku: Optional[str] = Field(default=None, description="商品SKU")
    barcode: Optional[str] = Field(default=None, description="商品条码")
    category_id: Optional[int] = Field(default=None, description="分类ID")
    price: Optional[float] = Field(default=None, description="商品价格")
    cost_price: Optional[float] = Field(default=None, description="成本价格")
    market_price: Optional[float] = Field(default=None, description="市场价格")
    currency: Optional[str] = Field(default=None, description="货币单位")
    status: Optional[str] = Field(default=None, description="商品状态")
    is_featured: Optional[bool] = Field(default=None, description="是否为推荐商品")
    is_digital: Optional[bool] = Field(default=None, description="是否为数字商品")
    track_inventory: Optional[bool] = Field(default=None, description="是否跟踪库存")
    stock_quantity: Optional[int] = Field(default=None, description="库存数量")
    min_stock_level: Optional[int] = Field(default=None, description="最低库存警戒线")
    max_stock_level: Optional[int] = Field(default=None, description="最高库存限制")
    weight: Optional[float] = Field(default=None, description="重量(kg)")
    dimensions: Optional[Dict[str, Any]] = Field(default=None, description="尺寸信息")
    attributes: Optional[Dict[str, Any]] = Field(default=None, description="商品属性")
    seo_title: Optional[str] = Field(default=None, description="SEO标题")
    seo_description: Optional[str] = Field(default=None, description="SEO描述")
    seo_keywords: Optional[str] = Field(default=None, description="SEO关键词")
    rich_description: Optional[str] = Field(default=None, description="产品富文本详情")

class ProductResponse(ProductBase):
    """商品响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "status": "active",
                "view_count": 1500,
                "sales_count": 50,
                "rating_average": 4.8,
                "rating_count": 25,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00",
                "rich_description": "<p>详细介绍内容</p>"
            }
        }
    )
    
    id: int = Field(description="商品ID")
    status: str = Field(description="商品状态")
    view_count: int = Field(description="浏览次数")
    sales_count: int = Field(description="销售次数")
    rating_average: float = Field(description="平均评分")
    rating_count: int = Field(description="评分次数")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    album: Optional[AlbumResponse] = Field(default=None, description="关联图册")
    rich_description: Optional[str] = Field(default=None, description="产品富文本详情")

class ProductListResponse(PaginatedResponse[ProductResponse]):
    """商品列表响应模型"""
    pass

class GetProductsParams(CamelCaseModel):
    """获取商品列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore'
    )
    
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    status: Optional[str] = Field(default=None, description="商品状态")
    category_id: Optional[int] = Field(default=None, description="分类ID")
    is_featured: Optional[bool] = Field(default=None, description="是否为推荐商品")
    search_term: Optional[str] = Field(default=None, description="搜索关键词")
    min_price: Optional[float] = Field(default=None, description="最低价格")
    max_price: Optional[float] = Field(default=None, description="最高价格")
    order_by: Optional[str] = Field(default="created_at", description="排序字段")
    order_desc: Optional[bool] = Field(default=True, description="是否降序")

class UserProductSpecCombination(CamelCaseModel):
    """用户端产品规格组合，仅用于用户端接口，减少冗余字段传输"""
    id: int = Field(..., description="规格组合ID")
    sku: str = Field(..., description="SKU")
    price: float = Field(..., description="价格")
    stock_quantity: int = Field(..., description="库存数量")
    spec_option_ids: List[int] = Field(..., description="该组合包含的所有规格值ID")
    spec_option_values: List[str] = Field(..., description="该组合包含的所有规格值")
    album_url: Optional[str] = Field(default=None, description="规格图片URL")

class UserProductSpec(CamelCaseModel):
    """用户端产品规格属性结构"""
    id: int = Field(..., description="规格ID")
    name: str = Field(..., description="规格名")
    options: List[dict] = Field(..., description="可选值列表，如[{id, value}]")

class UserProductResponse(CamelCaseModel):
    """用户端产品响应模型，列表用image_url，详情用album"""
    id: int
    name: str
    description: Optional[str] = None
    short_description: Optional[str] = None
    price: float
    currency: str
    image_url: Optional[str] = None  # 产品封面图片URL（列表用）
    album: Optional[UserAlbumResponse] = None  # 产品主图册（详情用，精简版）
    is_featured: bool
    attributes: Dict[str, Any]
    rich_description: Optional[str] = None
    specs: List[UserProductSpec]
    spec_combinations: List[UserProductSpecCombination]

class UserProductListResponse(PaginatedResponse[UserProductResponse]):
    """用户端商品分页响应模型"""
    pass

class ProductBatchUpdate(CamelCaseModel):
    """商品批量更新模型 - 只包含允许批量更新的字段"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "price": 999.00,
                "status": "active",
                "is_featured": True,
                "category_id": 2
            }
        }
    )

    price: Optional[float] = Field(default=None, description="商品价格")
    status: Optional[str] = Field(default=None, description="商品状态")
    is_featured: Optional[bool] = Field(default=None, description="是否为推荐商品")
    category_id: Optional[int] = Field(default=None, description="分类ID")

class BatchUpdateRequest(CamelCaseModel):
    """批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "product_ids": [1, 2, 3, 4, 5],
                "update_data": {
                    "price": 999.00,
                    "status": "active",
                    "is_featured": True
                }
            }
        }
    )

    product_ids: List[int] = Field(..., min_length=1, max_length=100, description="产品ID列表，最多100个")
    update_data: ProductBatchUpdate = Field(..., description="更新数据")

class BatchUpdateResponse(CamelCaseModel):
    """批量更新响应模型"""
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "updated_count": 5,
                "failed_ids": [],
                "total_requested": 5
            }
        }
    )

    updated_count: int = Field(..., description="成功更新的产品数量")
    failed_ids: List[int] = Field(default_factory=list, description="更新失败的产品ID列表")
    total_requested: int = Field(..., description="请求更新的产品总数")
