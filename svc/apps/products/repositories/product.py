"""
商品数据访问层。
负责商品模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Any, Dict, List, Optional, Tuple, Union

from sqlalchemy import and_, asc, desc, func, or_, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from svc.apps.albums.models.album import Album
from svc.apps.products.models.category import Category
from svc.apps.products.models.product import Product, ProductStatus
from svc.apps.products.schemas.product import ProductCreate, ProductUpdate
from svc.core.repositories import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class ProductRepository(BaseRepository[Product, ProductCreate, ProductUpdate]):
    """商品仓库类，提供商品数据访问方法
    
    该仓库类实现了Product模型的数据访问操作，
    包括商品的基本CRUD操作以及特定的数据查询和统计功能。
    """
    
    def __init__(self, db: AsyncSession):
        """初始化商品仓库"""
        super().__init__(db, Product)
    
    async def create_with_album(self, product_data: ProductCreate, album_id: int) -> Product:
        """创建商品并关联图册ID"""
        new_product = Product(**product_data.model_dump(), album_id=album_id)
        self.db.add(new_product)
        await self.db.flush()
        await self.db.refresh(new_product)
        return new_product

    async def get_one(
        self, 
        **filters
    ) -> Optional[Product]:
        """根据条件获取单个实体，并预加载图册"""
        if not filters:
            return None
        
        query = select(self.model).options(selectinload(self.model.album))
        query = self._apply_filters(query, **filters)
        
        result = await self.db.execute(query)
        return result.scalars().first()

    async def get_list(
        self, 
        *,
        skip: int = 0, 
        limit: int = 100,
        order_by: Optional[Union[str, 'Column']] = None,
        order_direction: str = "asc",
        **filters
    ) -> List[Product]:
        """获取实体列表，并预加载图册"""
        query = self._build_query(order_by, order_direction, **filters).options(selectinload(self.model.album))
        
        query = query.offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def get_by_sku(self, sku: str) -> Optional[Product]:
        """通过SKU获取商品
        
        Args:
            sku: 商品SKU
            
        Returns:
            Optional[Product]: 商品对象，不存在则返回None
        """
        return await self.get_one(sku=sku)
    
    async def get_by_barcode(self, barcode: str) -> Optional[Product]:
        """通过条码获取商品
        
        Args:
            barcode: 商品条码
            
        Returns:
            Optional[Product]: 商品对象，不存在则返回None
        """
        return await self.get_one(barcode=barcode)
    
    async def get_products(
        self,
        skip: int = 0,
        limit: int = 10,
        status: Optional[str] = None,
        category_id: Optional[int] = None,
        is_featured: Optional[bool] = None,
        search_term: Optional[str] = None,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None,
        order_by: str = "created_at",
        order_desc: bool = True
    ) -> Tuple[List[Product], int]:
        """获取商品列表及总数
        
        Args:
            skip: 跳过记录数
            limit: 返回记录数
            status: 商品状态筛选
            category_id: 分类ID筛选
            is_featured: 是否推荐商品筛选
            search_term: 搜索关键词
            min_price: 最低价格
            max_price: 最高价格
            order_by: 排序字段
            order_desc: 是否降序排序
            
        Returns:
            Tuple[List[Product], int]: 商品列表和总记录数
        """
        filters = {}
        if status:
            filters["status"] = status
        if category_id:
            filters["category_id"] = category_id
        if is_featured is not None:
            filters["is_featured"] = is_featured
            
        # 使用基类的方法
        if search_term or min_price is not None or max_price is not None:
            # 包含搜索条件时，需要自定义查询
            query = select(self.model).options(
                selectinload(self.model.album).selectinload(Album.cover_image)
            )
            
            # 添加过滤条件
            conditions = []
            if status:
                conditions.append(self.model.status == status)
            if category_id:
                conditions.append(self.model.category_id == category_id)
            if is_featured is not None:
                conditions.append(self.model.is_featured == is_featured)
            
            # 添加价格范围条件
            if min_price is not None:
                conditions.append(self.model.price >= min_price)
            if max_price is not None:
                conditions.append(self.model.price <= max_price)
            
            # 添加搜索条件
            if search_term:
                conditions.append(
                    or_(
                        self.model.name.ilike(f"%{search_term}%"),
                        self.model.description.ilike(f"%{search_term}%"),
                        self.model.sku.ilike(f"%{search_term}%")
                    )
                )
            
            if conditions:
                query = query.where(and_(*conditions))
            
            # 添加排序
            if order_by:
                if hasattr(self.model, order_by):
                    order_column = getattr(self.model, order_by)
                    if order_desc:
                        query = query.order_by(desc(order_column))
                    else:
                        query = query.order_by(asc(order_column))
            
            # 添加分页
            query = query.offset(skip).limit(limit)
            result = await self.db.execute(query)
            products = result.scalars().all()
            
            # 获取总数的查询
            count_query = select(func.count()).select_from(self.model)
            if conditions:
                count_query = count_query.where(and_(*conditions))
            count_result = await self.db.execute(count_query)
            total = count_result.scalar() or 0
            
            return products, total
        else:
            # 无搜索条件时，直接使用get_list获取列表和count获取总数
            items = await self.get_list(
                skip=skip,
                limit=limit,
                order_by=order_by,
                order_direction="desc" if order_desc else "asc",
                **filters
            )
            total = await self.count(**filters)
            return items, total
    
    async def get_by_category_id(
        self, 
        category_id: int, 
        page_num: int = 1, 
        page_size: int = 100, 
        order_by: str = "created_at", 
        order_desc: bool = True
    ) -> Tuple[List[Product], int]:
        """获取指定分类的所有商品（分页）
        
        Args:
            category_id: 分类ID
            page_num: 页码
            page_size: 每页数量
            order_by: 排序字段
            order_desc: 是否降序
            
        Returns:
            Tuple[List[Product], int]: 商品列表和总数
        """
        return await self.get_paginated(
            page=page_num, 
            page_size=page_size, 
            order_by=order_by, 
            order_direction="desc" if order_desc else "asc", 
            category_id=category_id
        )
    
    async def get_featured_products(
        self, 
        limit: int = 10, 
        skip: int = 0
    ) -> Tuple[List[Product], int]:
        """获取所有推荐商品（分页）
        
        Args:
            limit: 返回结果数量限制
            skip: 结果偏移量
            
        Returns:
            Tuple[List[Product], int]: 商品列表和总数
        """
        conditions = [
            self.model.status == ProductStatus.ACTIVE,
            self.model.is_featured == True
        ]
        
        # Get paginated list
        query = select(self.model).where(
            *conditions
        ).order_by(desc(self.model.created_at)).offset(skip).limit(limit).options(selectinload(self.model.album))
        result = await self.db.execute(query)
        products = result.scalars().all()
        
        # Get total count
        count_query = select(func.count()).select_from(self.model).where(*conditions)
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return products, total
    
    async def get_low_stock_products(
        self, 
        limit: int = 10, 
        skip: int = 0
    ) -> Tuple[List[Product], int]:
        """获取库存不足的商品
        
        Args:
            limit: 返回结果数量限制
            skip: 结果偏移量
            
        Returns:
            Tuple[List[Product], int]: 商品列表和总数
        """
        conditions = [
            self.model.track_inventory == True,
            self.model.stock_quantity <= self.model.min_stock_level
        ]
        
        # Get paginated list
        query = select(self.model).where(
            *conditions
        ).order_by(asc(self.model.stock_quantity)).offset(skip).limit(limit).options(selectinload(self.model.album))
        result = await self.db.execute(query)
        products = result.scalars().all()
        
        # Get total count
        count_query = select(func.count()).select_from(self.model).where(*conditions)
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return products, total
    
    async def increment_view_count(self, product_id: int) -> Optional[Product]:
        """增加商品浏览次数
        
        Args:
            product_id: 商品ID
            
        Returns:
            Optional[Product]: 更新后的商品对象，不存在则返回None
        """
        product = await self.get_by_id(product_id)
        if not product:
            return None
            
        update_data = {"view_count": product.view_count + 1}
        return await self.update(product, update_data)
    
    async def increment_sales_count(self, product_id: int, quantity: int = 1) -> Optional[Product]:
        """增加商品销售次数
        
        Args:
            product_id: 商品ID
            quantity: 销售数量
            
        Returns:
            Optional[Product]: 更新后的商品对象，不存在则返回None
        """
        product = await self.get_by_id(product_id)
        if not product:
            return None
            
        update_data = {"sales_count": product.sales_count + quantity}
        return await self.update(product, update_data)
    
    async def update_stock_quantity(
        self, 
        product_id: int, 
        quantity_change: int
    ) -> Optional[Product]:
        """更新商品库存数量
        
        Args:
            product_id: 商品ID
            quantity_change: 库存变化量（正数为增加，负数为减少）
            
        Returns:
            Optional[Product]: 更新后的商品对象，不存在则返回None
        """
        product = await self.get_by_id(product_id)
        if not product:
            return None
            
        new_quantity = max(0, product.stock_quantity + quantity_change)
        update_data = {"stock_quantity": new_quantity}
        return await self.update(product, update_data)
    
    async def update_rating(
        self, 
        product_id: int, 
        new_rating: float
    ) -> Optional[Product]:
        """更新商品评分
        
        Args:
            product_id: 商品ID
            new_rating: 新评分
            
        Returns:
            Optional[Product]: 更新后的商品对象，不存在则返回None
        """
        product = await self.get_by_id(product_id)
        if not product:
            return None
        
        # 计算新的平均评分
        total_rating = product.rating_average * product.rating_count + new_rating
        new_count = product.rating_count + 1
        new_average = total_rating / new_count
        
        update_data = {
            "rating_average": round(new_average, 2),
            "rating_count": new_count
        }
        return await self.update(product, update_data)

    async def get_by_id(self, id: int) -> Optional[Product]:
        query = select(self.model).where(self.model.id == id).options(
            selectinload(self.model.album).selectinload(Album.cover_image)
        )
        result = await self.db.execute(query)
        return result.scalars().first()

    async def get_existing_ids(self, product_ids: List[int]) -> List[int]:
        """获取存在的产品ID列表

        Args:
            product_ids: 产品ID列表

        Returns:
            List[int]: 存在的产品ID列表
        """
        if not product_ids:
            return []

        query = select(self.model.id).where(self.model.id.in_(product_ids))
        result = await self.db.execute(query)
        return result.scalars().all()

    async def batch_update(self, product_ids: List[int], update_data: Dict[str, Any]) -> int:
        """批量更新产品

        Args:
            product_ids: 产品ID列表
            update_data: 更新数据字典

        Returns:
            int: 更新的记录数
        """
        if not product_ids or not update_data:
            return 0

        # 过滤掉None值和空值
        filtered_data = {k: v for k, v in update_data.items() if v is not None}
        if not filtered_data:
            return 0

        # 添加更新时间
        filtered_data['updated_at'] = get_utc_now_without_tzinfo()

        # 执行批量更新
        stmt = update(self.model).where(
            self.model.id.in_(product_ids)
        ).values(**filtered_data)

        result = await self.db.execute(stmt)
        await self.db.flush()

        return result.rowcount
